# ES7210 驱动修改总结

## 修改目标

将ES7210驱动中的 `ES7210_CHANNELS_MAX` 和 `ES7210_WORK_MODE` 两个硬编码宏定义改为可以从DTS文件动态配置的参数。

## 主要修改内容

### 1. 结构体修改

在 `es7210_priv` 结构体中添加了两个新字段：
```c
struct es7210_priv {
    // ... 原有字段 ...
    unsigned int channels_max;  /* Maximum channels from DTS */
    unsigned int work_mode;     /* Work mode from DTS */
    // ... 原有字段 ...
};
```

### 2. 新增函数

#### 2.1 `es7210_get_adc_dev_num()`
```c
static int es7210_get_adc_dev_num(u32 channels_max)
```
根据通道数动态计算所需的ADC设备数量。

#### 2.2 `es7210_parse_dt()`
```c
static int es7210_parse_dt(struct device *dev, struct es7210_priv *es7210)
```
解析DTS中的配置属性：
- `everest,channels-max`: 最大通道数
- `everest,work-mode`: 工作模式

#### 2.3 `es7210_multi_chips_write_n()` 和 `es7210_multi_chips_update_bits_n()`
```c
static int es7210_multi_chips_write_n(u8 reg, unsigned char value, int adc_dev_num)
static int es7210_multi_chips_update_bits_n(u8 reg, u8 mask, u8 value, int adc_dev_num)
```
支持指定ADC设备数量的多芯片操作函数。

### 3. 函数修改

#### 3.1 `es7210_tdm_init_codec()`
- 修改函数签名：`static void es7210_tdm_init_codec(u8 mode, u32 channels_max)`
- 使用动态计算的 `adc_dev_num` 替代硬编码的 `ADC_DEV_MAXNUM`
- 所有循环中的 `ADC_DEV_MAXNUM` 都改为 `adc_dev_num`

#### 3.2 `es7210_i2c_probe()`
- 添加DTS属性解析调用
- 添加ADC设备数量验证
- 使用DTS配置的值初始化 `tdm_mode`

#### 3.3 `es7210_pcm_hw_params()`
- 获取 `es7210_priv` 结构体
- 使用动态计算的 `adc_dev_num`

#### 3.4 `es7210_suspend()`
- 获取 `es7210_priv` 结构体
- 使用动态计算的 `adc_dev_num`

#### 3.5 `es7210_resume()` 和 `es7210_probe()`
- 传递 `channels_max` 参数给 `es7210_tdm_init_codec()`

### 4. DTS属性定义

#### 4.1 支持的属性

| 属性名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| `everest,channels-max` | u32 | 最大通道数 (2,4,6,8,10,12,14,16,32) | 32 |
| `everest,work-mode` | u32 | 工作模式 (0-8) | 2 |

#### 4.2 工作模式值

| 值 | 模式 | 说明 |
|----|------|------|
| 0 | ES7210_TDM_1LRCK_DSPA | DSP-A，单LRCK |
| 1 | ES7210_TDM_1LRCK_DSPB | DSP-B，单LRCK |
| 2 | ES7210_TDM_1LRCK_I2S | I2S，单LRCK |
| 3 | ES7210_TDM_1LRCK_LJ | 左对齐，单LRCK |
| 4 | ES7210_TDM_NLRCK_DSPA | DSP-A，多LRCK |
| 5 | ES7210_TDM_NLRCK_DSPB | DSP-B，多LRCK |
| 6 | ES7210_TDM_NLRCK_I2S | I2S，多LRCK |
| 7 | ES7210_TDM_NLRCK_LJ | 左对齐，多LRCK |
| 8 | ES7210_NORMAL_I2S | 普通I2S |

### 5. 兼容性

- **向后兼容**: 如果DTS中没有指定新属性，使用原来的默认值
- **验证机制**: 对DTS中的值进行有效性检查
- **错误处理**: 无效配置会导致probe失败并输出错误信息

### 6. 调试支持

驱动会输出详细的配置信息：
```
es7210: channels-max from DTS: 8
es7210: work-mode from DTS: 2
es7210: Enter es7210_tdm_init_codec, mode: 2, channels_max: 8, adc_dev_num: 2
```

## 使用示例

### DTS配置示例
```dts
es7210_8ch: es7210@40 {
    compatible = "ES7210_MicArray_0";
    reg = <0x40>;
    everest,channels-max = <8>;
    everest,work-mode = <2>;  /* I2S with single LRCK */
    clocks = <&cru SCLK_I2S1_8CH>;
    clock-names = "mclk";
};
```

### 通道数与ADC设备数对应关系

| 通道数 | ADC设备数 |
|--------|-----------|
| 2-4    | 1         |
| 6-8    | 2         |
| 10-12  | 3         |
| 14-16  | 4         |
| 32     | 8         |

## 注意事项

1. **静态数组大小**: `ADC_DEV_MAXNUM` 仍然作为静态数组的最大大小保留
2. **运行时检查**: 实际使用的设备数量通过 `es7210_get_adc_dev_num()` 动态计算
3. **多设备配置**: 对于需要多个ES7210设备的配置，需要在DTS中配置相应数量的设备节点
4. **时钟配置**: 确保为每个ES7210设备正确配置MCLK时钟源

## 测试建议

1. 测试不同通道数配置 (2, 4, 8, 16, 32)
2. 测试不同工作模式 (I2S, DSP-A, DSP-B等)
3. 测试默认值兼容性 (不配置DTS属性)
4. 测试无效值处理 (错误的通道数或工作模式)
5. 测试多设备配置场景
