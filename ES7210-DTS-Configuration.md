# ES7210 Device Tree Configuration Guide

## 概述

ES7210 codec驱动现在支持通过Device Tree (DTS) 文件配置通道数和工作模式，而不需要修改源代码中的宏定义。

## 修改内容

### 1. 新增的DTS属性

- `everest,channels-max`: 配置最大通道数
- `everest,work-mode`: 配置工作模式

### 2. 代码修改

#### 2.1 结构体修改
- 在 `es7210_priv` 结构体中添加了 `channels_max`、`work_mode`、`dai_driver` 和 `controls` 字段

#### 2.2 新增函数
- `es7210_parse_dt()`: 解析DTS属性
- `es7210_get_adc_dev_num()`: 动态计算ADC设备数量
- `es7210_create_dai_driver()`: 动态创建DAI驱动
- `es7210_free_dai_driver()`: 释放动态创建的DAI驱动
- `es7210_create_controls()`: 动态创建控制接口（当前为简化版本）
- `es7210_free_controls()`: 释放动态创建的控制接口
- `es7210_multi_chips_write_n()` 和 `es7210_multi_chips_update_bits_n()`: 支持指定设备数量的操作

#### 2.3 架构改进
- **动态DAI注册**: 根据通道数动态创建和注册DAI驱动
- **智能设备管理**: 只在第一个I2C设备上注册音频组件，其他设备作为辅助ADC
- **运行时配置**: 所有配置都在运行时根据DTS确定，无需重新编译

## DTS配置说明

### 支持的通道数 (everest,channels-max)

| 通道数 | ADC设备数 | 说明 |
|--------|-----------|------|
| 2      | 1         | 2通道立体声 |
| 4      | 1         | 4通道 |
| 6      | 2         | 6通道 |
| 8      | 2         | 8通道 |
| 10     | 3         | 10通道 |
| 12     | 3         | 12通道 |
| 14     | 4         | 14通道 |
| 16     | 4         | 16通道 |
| 32     | 8         | 32通道 |

### 支持的工作模式 (everest,work-mode)

| 值 | 模式名称 | 说明 |
|----|----------|------|
| 0  | ES7210_TDM_1LRCK_DSPA | DSP-A格式，单LRCK |
| 1  | ES7210_TDM_1LRCK_DSPB | DSP-B格式，单LRCK |
| 2  | ES7210_TDM_1LRCK_I2S  | I2S格式，单LRCK |
| 3  | ES7210_TDM_1LRCK_LJ   | 左对齐格式，单LRCK |
| 4  | ES7210_TDM_NLRCK_DSPA | DSP-A格式，多LRCK |
| 5  | ES7210_TDM_NLRCK_DSPB | DSP-B格式，多LRCK |
| 6  | ES7210_TDM_NLRCK_I2S  | I2S格式，多LRCK |
| 7  | ES7210_TDM_NLRCK_LJ   | 左对齐格式，多LRCK |
| 8  | ES7210_NORMAL_I2S     | 普通I2S模式 |

## 配置示例

### 8通道I2S配置
```dts
es7210_8ch: es7210@40 {
    compatible = "ES7210_MicArray_0";
    reg = <0x40>;
    everest,channels-max = <8>;
    everest,work-mode = <2>;  /* I2S with single LRCK */
    clocks = <&cru SCLK_I2S1_8CH>;
    clock-names = "mclk";
};
```

### 16通道TDM配置
```dts
es7210_16ch: es7210@41 {
    compatible = "ES7210_MicArray_0";
    reg = <0x41>;
    everest,channels-max = <16>;
    everest,work-mode = <6>;  /* I2S with multiple LRCK */
    clocks = <&cru SCLK_I2S1_8CH>;
    clock-names = "mclk";
};
```

### 4通道DSP-A配置
```dts
es7210_4ch: es7210@42 {
    compatible = "ES7210_MicArray_0";
    reg = <0x42>;
    everest,channels-max = <4>;
    everest,work-mode = <0>;  /* DSP-A with single LRCK */
    clocks = <&cru SCLK_I2S1_8CH>;
    clock-names = "mclk";
};
```

## 默认值

如果DTS中没有指定这些属性，驱动将使用以下默认值：
- `everest,channels-max`: 32 (MIC_CHN_32)
- `everest,work-mode`: 2 (ES7210_TDM_1LRCK_I2S)

## 注意事项

1. **兼容性**: 修改后的驱动仍然向后兼容，如果DTS中没有指定新属性，将使用原来的默认值。

2. **验证**: 驱动会验证DTS中配置的值是否有效，无效值会导致probe失败。

3. **多设备**: 对于需要多个ES7210设备的配置（如32通道需要8个设备），需要在DTS中配置相应数量的设备节点。

4. **时钟配置**: 确保为每个ES7210设备正确配置MCLK时钟源。

## 调试信息

驱动会在probe和初始化时输出配置信息，可以通过dmesg查看：
```bash
dmesg | grep es7210
```

输出示例：
```
es7210: channels-max from DTS: 8
es7210: work-mode from DTS: 2
es7210: Enter es7210_tdm_init_codec, mode: 2, channels_max: 8, adc_dev_num: 2
```
