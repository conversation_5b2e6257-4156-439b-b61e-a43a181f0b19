/*
 * ES7210 Device Tree Example
 * 
 * This example shows how to configure ES7210 codec with different
 * channel counts and work modes through device tree properties.
 */

/ {
	/* Example 1: 8-channel configuration with I2S mode */
	es7210_8ch: es7210-8ch@40 {
		compatible = "ES7210_MicArray_0";
		reg = <0x40>;
		
		/* Configure for 8 channels maximum */
		everest,channels-max = <8>;
		
		/* Set work mode to TDM 1LRCK I2S mode */
		everest,work-mode = <2>;  /* ES7210_TDM_1LRCK_I2S */
		
		/* Optional: MCLK configuration */
		clocks = <&cru SCLK_I2S1_8CH>;
		clock-names = "mclk";
	};

	/* Example 2: 4-channel configuration with DSP-A mode */
	es7210_4ch: es7210-4ch@41 {
		compatible = "ES7210_MicArray_0";
		reg = <0x41>;
		
		/* Configure for 4 channels maximum */
		everest,channels-max = <4>;
		
		/* Set work mode to TDM 1LRCK DSP-A mode */
		everest,work-mode = <0>;  /* ES7210_TDM_1LRCK_DSPA */
		
		clocks = <&cru SCLK_I2S1_8CH>;
		clock-names = "mclk";
	};

	/* Example 3: 16-channel configuration with multiple LRCK I2S mode */
	es7210_16ch: es7210-16ch@42 {
		compatible = "ES7210_MicArray_0";
		reg = <0x42>;
		
		/* Configure for 16 channels maximum */
		everest,channels-max = <16>;
		
		/* Set work mode to TDM NLRCK I2S mode (multiple LRCK) */
		everest,work-mode = <6>;  /* ES7210_TDM_NLRCK_I2S */
		
		clocks = <&cru SCLK_I2S1_8CH>;
		clock-names = "mclk";
	};

	/* Example 4: 32-channel configuration */
	es7210_32ch: es7210-32ch@43 {
		compatible = "ES7210_MicArray_0";
		reg = <0x43>;
		
		/* Configure for 32 channels maximum */
		everest,channels-max = <32>;
		
		/* Set work mode to TDM NLRCK I2S mode */
		everest,work-mode = <6>;  /* ES7210_TDM_NLRCK_I2S */
		
		clocks = <&cru SCLK_I2S1_8CH>;
		clock-names = "mclk";
	};

	/* Example 5: Normal I2S mode (2 channels) */
	es7210_2ch: es7210-2ch@44 {
		compatible = "ES7210_MicArray_0";
		reg = <0x44>;
		
		/* Configure for 2 channels maximum */
		everest,channels-max = <2>;
		
		/* Set work mode to Normal I2S mode */
		everest,work-mode = <8>;  /* ES7210_NORMAL_I2S */
		
		clocks = <&cru SCLK_I2S1_8CH>;
		clock-names = "mclk";
	};
};

/*
 * Work Mode Values:
 * 0 - ES7210_TDM_1LRCK_DSPA    (DSP-A with single LRCK)
 * 1 - ES7210_TDM_1LRCK_DSPB    (DSP-B with single LRCK)
 * 2 - ES7210_TDM_1LRCK_I2S     (I2S with single LRCK)
 * 3 - ES7210_TDM_1LRCK_LJ      (Left Justified with single LRCK)
 * 4 - ES7210_TDM_NLRCK_DSPA    (DSP-A with multiple LRCK)
 * 5 - ES7210_TDM_NLRCK_DSPB    (DSP-B with multiple LRCK)
 * 6 - ES7210_TDM_NLRCK_I2S     (I2S with multiple LRCK)
 * 7 - ES7210_TDM_NLRCK_LJ      (Left Justified with multiple LRCK)
 * 8 - ES7210_NORMAL_I2S        (Normal I2S mode)
 *
 * Channel Values:
 * 2, 4, 6, 8, 10, 12, 14, 16, 32
 *
 * Note: The number of ADC devices will be automatically calculated:
 * - 2-4 channels:   1 ADC device
 * - 6-8 channels:   2 ADC devices
 * - 10-12 channels: 3 ADC devices
 * - 14-16 channels: 4 ADC devices
 * - 32 channels:    8 ADC devices
 */
