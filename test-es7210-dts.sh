#!/bin/bash

# ES7210 DTS Configuration Test Script
# This script helps test different ES7210 configurations

echo "ES7210 DTS Configuration Test Script"
echo "===================================="

# Function to show ES7210 debug information
show_es7210_info() {
    echo "Current ES7210 configuration:"
    if [ -d "/sys/kernel/debug/asoc" ]; then
        find /sys/kernel/debug/asoc -name "*es7210*" -type d | while read dir; do
            echo "Found ES7210 component: $dir"
            if [ -f "$dir/dapm_pop_time" ]; then
                echo "  DAPM pop time: $(cat $dir/dapm_pop_time)"
            fi
        done
    fi
    
    # Check dmesg for ES7210 messages
    echo ""
    echo "Recent ES7210 kernel messages:"
    dmesg | grep -i es7210 | tail -10
}

# Function to test different channel configurations
test_channel_config() {
    local channels=$1
    echo ""
    echo "Testing $channels channel configuration..."
    
    # Calculate expected ADC device number
    case $channels in
        2|4) expected_adc=1 ;;
        6|8) expected_adc=2 ;;
        10|12) expected_adc=3 ;;
        14|16) expected_adc=4 ;;
        32) expected_adc=8 ;;
        *) expected_adc="unknown" ;;
    esac
    
    echo "  Expected ADC devices: $expected_adc"
    
    # Look for corresponding kernel messages
    dmesg | grep -i "channels_max: $channels" | tail -1
    dmesg | grep -i "adc_dev_num: $expected_adc" | tail -1
}

# Function to test work mode configurations
test_work_mode() {
    local mode=$1
    local mode_name=$2
    echo ""
    echo "Testing work mode $mode ($mode_name)..."
    
    # Look for corresponding kernel messages
    dmesg | grep -i "work-mode from DTS: $mode" | tail -1
    dmesg | grep -i "mode: $mode" | tail -1
}

# Main test function
run_tests() {
    echo ""
    echo "Running ES7210 configuration tests..."
    
    # Test different channel configurations
    echo ""
    echo "=== Channel Configuration Tests ==="
    test_channel_config 2
    test_channel_config 4
    test_channel_config 8
    test_channel_config 16
    test_channel_config 32
    
    # Test different work modes
    echo ""
    echo "=== Work Mode Configuration Tests ==="
    test_work_mode 0 "ES7210_TDM_1LRCK_DSPA"
    test_work_mode 1 "ES7210_TDM_1LRCK_DSPB"
    test_work_mode 2 "ES7210_TDM_1LRCK_I2S"
    test_work_mode 3 "ES7210_TDM_1LRCK_LJ"
    test_work_mode 4 "ES7210_TDM_NLRCK_DSPA"
    test_work_mode 5 "ES7210_TDM_NLRCK_DSPB"
    test_work_mode 6 "ES7210_TDM_NLRCK_I2S"
    test_work_mode 7 "ES7210_TDM_NLRCK_LJ"
    test_work_mode 8 "ES7210_NORMAL_I2S"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [option]"
    echo "Options:"
    echo "  info     - Show current ES7210 information"
    echo "  test     - Run configuration tests"
    echo "  help     - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 info   # Show current ES7210 status"
    echo "  $0 test   # Run all tests"
}

# Function to check if ES7210 driver is loaded
check_driver() {
    if lsmod | grep -q snd_soc_es7210; then
        echo "✓ ES7210 driver is loaded"
        return 0
    else
        echo "✗ ES7210 driver is not loaded"
        echo "  Try: modprobe snd-soc-es7210"
        return 1
    fi
}

# Function to show DTS examples
show_dts_examples() {
    echo ""
    echo "=== DTS Configuration Examples ==="
    echo ""
    echo "8-channel I2S configuration:"
    cat << 'EOF'
es7210_8ch: es7210@40 {
    compatible = "ES7210_MicArray_0";
    reg = <0x40>;
    everest,channels-max = <8>;
    everest,work-mode = <2>;  /* I2S with single LRCK */
    clocks = <&cru SCLK_I2S1_8CH>;
    clock-names = "mclk";
};
EOF
    
    echo ""
    echo "16-channel TDM configuration:"
    cat << 'EOF'
es7210_16ch: es7210@41 {
    compatible = "ES7210_MicArray_0";
    reg = <0x41>;
    everest,channels-max = <16>;
    everest,work-mode = <6>;  /* I2S with multiple LRCK */
    clocks = <&cru SCLK_I2S1_8CH>;
    clock-names = "mclk";
};
EOF
}

# Main script logic
case "$1" in
    "info")
        check_driver
        show_es7210_info
        ;;
    "test")
        check_driver && run_tests
        ;;
    "examples")
        show_dts_examples
        ;;
    "help"|"")
        show_usage
        ;;
    *)
        echo "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac

echo ""
echo "Script completed."
